import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom';

import { designsTableConfig, defaultTableConfig } from '@constants';
import { useDataTableContext } from '@contexts/DataTableContext';
import { useDeleteTemplate } from '@quires/template';
import { useLayout } from '@contexts/LayoutContext';

import { DataTable } from 'primereact/datatable';
import { Tooltip } from 'primereact/tooltip';
import { Column } from 'primereact/column';
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';

import { TfiTrash } from "react-icons/tfi";
import { FiEdit } from 'react-icons/fi';
import { FaSearch } from 'react-icons/fa';
import { HiDotsVertical } from 'react-icons/hi';

import { createPortal } from 'react-dom';

function TemplatesDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, dataHandler, loading } = useDataTableContext();
    const { isMobile } = useLayout();
    const navigate = useNavigate();

    const deleteTemplate = useDeleteTemplate()
    const [searchQuery, setSearchQuery] = useState('');
    const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);

    useEffect(() => {
        setLazyParams({ ...defaultTableConfig, ...designsTableConfig })
    }, [])

    // Add debounced search handler
    useEffect(() => {
        const timeout = setTimeout(() => {
            setLazyParams(prev => ({
                ...prev,
                filters: {
                    ...prev.filters,
                    name: { value: searchQuery, matchMode: 'contains' }
                }
            }));
        }, 300);

        return () => clearTimeout(timeout);
    }, [searchQuery, setLazyParams]);

    const handleDeleteTemplateClick = (template) => {
        confirmDialog({
            message: `Are you sure you want to delete the template "${template.name || 'this template'}"?`,
            header: 'Delete Template Confirmation',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Yes, Delete',
            rejectLabel: 'Cancel',
            accept: () => deleteAdHandler(template.id),
        });
    };

    const deleteAdHandler = async (id) => {
        await deleteTemplate.mutateAsync({ id: id }, {
            onSuccess: () => {
                setLazyParams(prev => ({ ...prev }))
            }
        })
    }

    // Mobile action menu component
    const MobileActionMenu = ({ template, isOpen, onClose }) => {
        if (!isOpen) return null;

        return createPortal(
            <div
                className="fixed inset-0 bg-black bg-opacity-60 z-[9999] flex items-center justify-center"
                style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    backdropFilter: 'blur(2px)'
                }}
                onClick={onClose}
            >
                <div
                    className="bg-white rounded-lg p-4 m-4 w-full max-w-sm relative shadow-2xl"
                    style={{
                        zIndex: 10000,
                        backgroundColor: '#ffffff',
                        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5)',
                        border: '1px solid rgba(0, 0, 0, 0.1)'
                    }}
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="flex items-center mb-4 border-b pb-3">
                        <div className="w-10 h-10 rounded-full bg-purple-500 flex items-center justify-center mr-3">
                            <span className="text-white font-bold">{template.name?.charAt(0)?.toUpperCase()}</span>
                        </div>
                        <div>
                            <h3 className="font-semibold">{template.name}</h3>
                            <p className="text-sm text-gray-500">{template.card_type_name}</p>
                        </div>
                    </div>

                    <div className="space-y-2 bg-gray-50 p-2 rounded-lg">
                        {/* Edit */}
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-green-50 rounded-lg border border-gray-200"
                            onClick={() => {
                                navigate(`/manager/design-space/${template.id}`);
                                onClose();
                            }}
                        >
                            <FiEdit className="mr-3 text-green-500" size={18} />
                            <span>Edit Template</span>
                        </button>

                        {/* Delete */}
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-red-50 rounded-lg border border-gray-200 text-red-600"
                            onClick={() => {
                                handleDeleteTemplateClick(template);
                                onClose();
                            }}
                        >
                            <TfiTrash className="mr-3" size={18} />
                            <span>Delete Template</span>
                        </button>
                    </div>

                    <button
                        className="w-full mt-4 p-2 bg-gray-200 hover:bg-gray-300 rounded-lg text-center font-medium transition-colors"
                        onClick={onClose}
                    >
                        Cancel
                    </button>
                </div>
            </div>,
            document.body
        );
    };

    // Mobile list view component
    const MobileListView = () => {
        if (loading) {
            return (
                <div className="space-y-2">
                    {[...Array(5)].map((_, index) => (
                        <div key={index} className="bg-white border rounded-lg p-4 shadow-sm animate-pulse">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center flex-1">
                                    <div className="w-12 h-12 bg-gray-300 rounded-lg mr-3"></div>
                                    <div className="flex-1">
                                        <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                                        <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                                    </div>
                                </div>
                                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                            </div>
                        </div>
                    ))}
                </div>
            );
        }

        if (!data || data.length === 0) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No templates found</p>
                </div>
            );
        }

        return (
            <div className="space-y-2">
                {data.map((template) => (
                    <div key={template.id} className="bg-white border rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center flex-1">
                                <div className="w-12 h-12 rounded-lg bg-purple-500 flex items-center justify-center mr-3">
                                    <span className="text-white font-bold text-lg">
                                        {template.name?.charAt(0)?.toUpperCase()}
                                    </span>
                                </div>
                                <div className="flex-1">
                                    <h3 className="font-semibold text-gray-900">{template.name}</h3>
                                    <p className="text-sm text-gray-500">{template.card_type_name}</p>
                                </div>
                            </div>
                            <button
                                className="p-2 hover:bg-gray-100 rounded-full"
                                onClick={() => setMobileActionMenuOpen(template.id)}
                            >
                                <HiDotsVertical className="text-gray-500" size={20} />
                            </button>
                        </div>
                    </div>
                ))}

                {/* Mobile Action Menu */}
                {mobileActionMenuOpen && (
                    <MobileActionMenu
                        template={data.find(t => t.id === mobileActionMenuOpen)}
                        isOpen={!!mobileActionMenuOpen}
                        onClose={() => setMobileActionMenuOpen(null)}
                    />
                )}
            </div>
        );
    };

    // Data Table Body Template for actions
    const actionBodyTemplate = (rowData) => {
        return (
            <div className="d-flex justify-center items-center w-full ms-auto">
                {/* Edit */}
                <Tooltip target={`.update-button-${rowData.id}`} showDelay={100} className="fs-8" />
                <button
                    className={`btn btn-sm btn-icon update-button-${rowData.id} me-3`}
                    data-pr-position="bottom"
                    data-pr-tooltip="Edit Template"
                    onClick={() => navigate(`/manager/design-space/${rowData.id}`)}>
                    <FiEdit size={20} />
                </button>

                {/* Delete  */}
                <Tooltip target={`.delete-button-${rowData.id}`} showDelay={100} className="fs-8" />
                <button
                    className={`btn btn-sm btn-icon userType delete-button-${rowData.id}`}
                    data-pr-position="bottom"
                    data-pr-tooltip="Delete"
                    onClick={() => { handleDeleteTemplateClick(rowData) }}
                >
                    <TfiTrash size={20} />
                </button>
            </div>
        );
    }

    return (
        <div className="w-full h-full flex flex-col">
            {/* Search Bar Section */}
            <div className={`w-full mb-4 mt-1 ${isMobile ? 'px-2' : 'flex justify-center items-center'}`}>
                <div className={`relative ${isMobile ? 'w-full' : 'flex-grow max-w-[700px]'}`}>
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
                    </div>
                    <input
                        type="text"
                        placeholder="Search by template name..."
                        className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md
                                    focus:outline-none focus:ring-2 focus:ring-blue-300
                                    focus:border-blue-300 transition-all duration-200"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </div>
            </div>

            {/* Conditional rendering for mobile vs desktop */}
            <div className="flex-grow h-full">
                {isMobile ? (
                    <MobileListView />
                ) : (
                    <div className='table-responsive text-nowrap flex-1 min-h-0'>
                        <DataTable
                            lazy
                            responsiveLayout="stack"
                            breakpoint="960px"
                            dataKey="id"
                            paginator
                            className="table w-full border"
                            value={data}
                            first={lazyParams?.first}
                            rows={lazyParams?.rows}
                            rowsPerPageOptions={[5, 25, 50, 100]}
                            totalRecords={totalRecords}
                            onPage={dataHandler}
                            onSort={dataHandler}
                            sortField={lazyParams?.sortField}
                            sortOrder={lazyParams?.sortOrder}
                            onFilter={dataHandler}
                            filters={lazyParams?.filters}
                            loading={loading}
                            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} products"
                            bodyClassName="d-flex justify-center"
                            scrollHeight="100%"
                        >
                            <Column field="name" header="Name" filter sortable />
                            <Column field="card_type_name" header="Card Type" filter sortable />
                            <Column body={actionBodyTemplate} header="Actions" exportable={false} />
                        </DataTable>
                    </div>
                )}
            </div>

            {/* Confirmation Dialog for template deletion */}
            <ConfirmDialog />
        </div>
    )
}

export default TemplatesDataTable
